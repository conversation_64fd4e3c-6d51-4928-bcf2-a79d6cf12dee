import os
from jinja2 import Template
from weasyprint import HTM<PERSON>, CSS
from typing import Dict

class ResumeGenerator:
    def __init__(self):
        self.template = self._load_template()
        self.css = self._load_css()
    
    def _load_template(self) -> Template:
        """Load the HTML template for the resume"""
        template_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - {{ data.name }}</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="resume">
        <!-- Header Section -->
        <header class="header">
            <h1 class="name">{{ data.name }}</h1>
            <div class="contact-info">
                <span class="email">📧 {{ data.email }}</span>
                <span class="phone">📱 {{ data.phone }}</span>
                <span class="location">📍 {{ data.location }}</span>
            </div>
        </header>

        <!-- Professional Summary -->
        {% if data.summary %}
        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="section-content">
                <div class="summary-text">{{ data.summary }}</div>
            </div>
        </section>
        {% endif %}

        <!-- Technical Skills -->
        {% if data.technical_skills %}
        <section class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="section-content">
                <div class="skills-grid">
                    {% for category, skills in data.technical_skills.items() %}
                    <div class="skills-category">
                        <strong>{{ category }}</strong>
                        <div class="skills-list">{{ skills | join(', ') }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% endif %}

        <!-- Certifications -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            <div class="section-content">
                <div class="cert-grid">
                    {% for provider_group in data.certifications | groupby('provider') %}
                    <div class="cert-group">
                        <strong>{{ provider_group.grouper }}</strong>
                        <div>
                            {% for cert in provider_group.list %}
                            <span class="cert-item">{{ cert.certification }}</span>{% if not loop.last %}, {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% endif %}

        <!-- Professional Experience -->
        {% if data.experience %}
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            <div class="section-content">
                {% for job in data.experience %}
                <div class="experience-item">
                    <div class="experience-header">
                        <span class="job-title">{{ job.title }}</span>
                        <span class="company"> – {{ job.company }}</span>
                        {% if job.start_date and job.end_date %}
                        <span class="duration">{{ job.start_date }} – {{ job.end_date }}</span>
                        {% endif %}
                    </div>
                    {% if job.responsibilities %}
                    <ul class="responsibilities">
                        {% for resp in job.responsibilities %}
                        <li>{{ resp }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}

        <!-- Honor Awards -->
        {% if data.honor_awards %}
        <section class="section">
            <h2 class="section-title">Honor Awards</h2>
            <div class="section-content">
                <ul class="awards-list">
                    {% for award in data.honor_awards %}
                    <li>{{ award }}</li>
                    {% endfor %}
                </ul>
            </div>
        </section>
        {% endif %}
    </div>
</body>
</html>
        """
        return Template(template_content)
    
    def _load_css(self) -> str:
        """Load CSS styles for the resume"""
        return """
        @page {
            size: letter;
            margin: 1in;
        }

        body {
            font-family: 'Calibri', 'Arial', sans-serif;
            font-size: 11pt;
            line-height: 1.15;
            color: #000000;
            margin: 0;
            padding: 0;
        }

        .resume {
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 0.2in;
        }

        .name {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 4pt;
            color: #1f4e79;
        }

        .contact-info {
            font-size: 11pt;
            line-height: 1.2;
        }

        .section {
            margin-bottom: 0.15in;
        }

        .section-title {
            font-size: 12pt;
            font-weight: bold;
            color: #1f4e79;
            text-transform: uppercase;
            margin-bottom: 6pt;
            border-bottom: 1pt solid #1f4e79;
            padding-bottom: 2pt;
        }

        .summary-text {
            font-size: 11pt;
            line-height: 1.15;
            text-align: justify;
            margin-bottom: 0;
        }

        .skills-category {
            margin-bottom: 6pt;
            line-height: 1.15;
        }

        .skills-category strong {
            font-weight: bold;
            color: #1f4e79;
        }

        .cert-group {
            margin-bottom: 6pt;
        }

        .cert-group strong {
            font-weight: bold;
            color: #1f4e79;
        }

        .cert-item {
            margin-left: 0;
            line-height: 1.15;
        }

        .experience-item {
            margin-bottom: 0.12in;
            break-inside: avoid;
        }

        .experience-header {
            margin-bottom: 2pt;
        }

        .job-title {
            font-size: 11pt;
            font-weight: bold;
            color: #1f4e79;
            display: inline;
        }

        .company {
            font-size: 11pt;
            font-weight: normal;
            display: inline;
        }

        .duration {
            font-size: 11pt;
            font-weight: normal;
            display: inline;
            float: right;
        }

        .responsibilities {
            margin: 4pt 0 0 0;
            padding-left: 0;
            list-style: none;
        }

        .responsibilities li {
            margin-bottom: 2pt;
            font-size: 11pt;
            line-height: 1.15;
            padding-left: 12pt;
            text-indent: -12pt;
        }

        .responsibilities li::before {
            content: '• ';
        }

        .awards-list {
            list-style: none;
            padding-left: 0;
            margin: 0;
        }

        .awards-list li {
            margin-bottom: 2pt;
            font-size: 11pt;
            line-height: 1.15;
            padding-left: 12pt;
            text-indent: -12pt;
        }

        .awards-list li::before {
            content: '• ';
        }
        """
    
    def generate_html(self, data: Dict) -> str:
        """Generate HTML resume from parsed data"""
        return self.template.render(data=data, css=self.css)

    def generate_pdf(self, html_content: str, filename: str) -> str:
        """Generate PDF from HTML content using WeasyPrint"""
        # Create uploads directory if it doesn't exist
        os.makedirs('uploads', exist_ok=True)

        # Full path for the PDF
        pdf_path = os.path.join('uploads', filename)

        try:
            # Generate PDF using WeasyPrint
            html_doc = HTML(string=html_content)
            html_doc.write_pdf(pdf_path)
            return pdf_path
        except Exception as e:
            print(f"WeasyPrint error: {e}")
            raise Exception(f"Failed to generate PDF: {str(e)}")
