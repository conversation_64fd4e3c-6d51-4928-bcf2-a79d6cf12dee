import os
import tempfile
from jinja2 import Template
from weasyprint import HTM<PERSON>, CSS
from typing import Dict

class ResumeGenerator:
    def __init__(self):
        self.template = self._load_template()
        self.css = self._load_css()
    
    def _load_template(self) -> Template:
        """Load the HTML template for the resume"""
        template_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - {{ data.title }}</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="resume">
        <!-- Header Section -->
        <header class="header">
            <h1 class="name">Your Name</h1>
            <div class="contact-info">
                <span class="email"><EMAIL></span>
                <span class="phone">+****************</span>
                <span class="location">{{ data.location }}</span>
            </div>
        </header>

        <!-- Professional Summary -->
        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="section-content">
                <p>{{ data.description }}</p>
            </div>
        </section>

        <!-- Target Position -->
        <section class="section">
            <h2 class="section-title">Target Position</h2>
            <div class="section-content">
                <div class="job-target">
                    <h3 class="job-title">{{ data.title }}</h3>
                    <p class="company">{{ data.company }}</p>
                    <p class="job-location">{{ data.location }}</p>
                </div>
            </div>
        </section>

        <!-- Core Competencies -->
        {% if data.requirements %}
        <section class="section">
            <h2 class="section-title">Core Competencies</h2>
            <div class="section-content">
                <ul class="competencies-list">
                    {% for req in data.requirements %}
                    <li>{{ req }}</li>
                    {% endfor %}
                </ul>
            </div>
        </section>
        {% endif %}

        <!-- Professional Experience -->
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            <div class="section-content">
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">Senior Security Engineer</h3>
                        <span class="duration">2020 - Present</span>
                    </div>
                    <p class="company">Previous Company Name</p>
                    {% if data.responsibilities %}
                    <ul class="responsibilities">
                        {% for resp in data.responsibilities %}
                        <li>{{ resp }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">Cybersecurity Analyst</h3>
                        <span class="duration">2018 - 2020</span>
                    </div>
                    <p class="company">Another Company</p>
                    <ul class="responsibilities">
                        <li>Monitored security events and incidents using SIEM tools</li>
                        <li>Conducted vulnerability assessments and penetration testing</li>
                        <li>Developed security policies and procedures</li>
                        <li>Collaborated with IT teams on security implementations</li>
                    </ul>
                </div>
            </div>
        </section>

        <!-- Education -->
        <section class="section">
            <h2 class="section-title">Education</h2>
            <div class="section-content">
                <div class="education-item">
                    <h3 class="degree">Bachelor of Science in Computer Science</h3>
                    <p class="school">University Name</p>
                    <span class="graduation-year">2018</span>
                </div>
            </div>
        </section>

        <!-- Certifications -->
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            <div class="section-content">
                <ul class="certifications-list">
                    <li>CISSP - Certified Information Systems Security Professional</li>
                    <li>CEH - Certified Ethical Hacker</li>
                    <li>AWS Certified Security - Specialty</li>
                    <li>CompTIA Security+</li>
                </ul>
            </div>
        </section>
    </div>
</body>
</html>
        """
        return Template(template_content)
    
    def _load_css(self) -> str:
        """Load CSS styles for the resume"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .resume {
            max-width: 8.5in;
            margin: 0 auto;
            padding: 0.75in;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #2c3e50;
        }

        .name {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            font-size: 1rem;
            color: #666;
        }

        .section {
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.75rem;
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #bdc3c7;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .section-content {
            padding-left: 0.5rem;
        }

        .job-target {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }

        .job-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .company {
            font-size: 1rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .job-location {
            font-size: 0.9rem;
            color: #888;
        }

        .competencies-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 0.5rem;
            list-style: none;
        }

        .competencies-list li {
            background: #ecf0f1;
            padding: 0.5rem;
            border-radius: 3px;
            font-size: 0.9rem;
        }

        .experience-item {
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .duration {
            font-size: 0.9rem;
            color: #666;
            font-weight: bold;
        }

        .responsibilities {
            margin-top: 0.5rem;
            padding-left: 1.5rem;
        }

        .responsibilities li {
            margin-bottom: 0.25rem;
            font-size: 0.95rem;
        }

        .education-item {
            margin-bottom: 1rem;
        }

        .degree {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .school {
            color: #666;
            margin-bottom: 0.25rem;
        }

        .graduation-year {
            font-size: 0.9rem;
            color: #888;
        }

        .certifications-list {
            list-style: none;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 0.5rem;
        }

        .certifications-list li {
            background: #e8f5e8;
            padding: 0.5rem;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }

        @media print {
            .resume {
                margin: 0;
                padding: 0.5in;
            }
            
            .section {
                page-break-inside: avoid;
            }
        }

        @page {
            size: letter;
            margin: 0.5in;
        }
        """
    
    def generate_html(self, data: Dict) -> str:
        """Generate HTML resume from parsed data"""
        return self.template.render(data=data, css=self.css)
    
    def generate_pdf(self, html_content: str, filename: str) -> str:
        """Generate PDF from HTML content"""
        # Create uploads directory if it doesn't exist
        os.makedirs('uploads', exist_ok=True)
        
        # Full path for the PDF
        pdf_path = os.path.join('uploads', filename)
        
        # Generate PDF using WeasyPrint
        HTML(string=html_content).write_pdf(pdf_path)
        
        return pdf_path
