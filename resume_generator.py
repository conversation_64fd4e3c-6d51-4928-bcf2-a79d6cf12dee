from jinja2 import Template
from typing import Dict

class ResumeGenerator:
    def __init__(self):
        self.template = self._load_template()
        self.css = self._load_css()
    
    def _load_template(self) -> Template:
        """Load the HTML template for the resume"""
        template_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - {{ data.name }}</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="resume">
        <!-- Header Section -->
        <header class="header">
            <h1 class="name">{{ data.name }}</h1>
            <div class="contact-info">
                <span class="email">{{ data.email }}</span>
                <span class="phone">{{ data.phone }}</span>
                <span class="location">{{ data.location }}</span>
            </div>
        </header>

        <!-- Professional Summary -->
        {% if data.summary %}
        <section class="section">
            <h2 class="section-title">Summary</h2>
            <div class="section-content">
                <p>{{ data.summary }}</p>
            </div>
        </section>
        {% endif %}

        <!-- Technical Skills -->
        {% if data.technical_skills %}
        <section class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="section-content">
                {% for category, skills in data.technical_skills.items() %}
                <div class="skills-category">
                    <strong>{{ category }}:</strong>
                    <span class="skills-list">{{ skills | join(', ') }}</span>
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}

        <!-- Certifications -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            <div class="section-content">
                {% for provider_group in data.certifications | groupby('provider') %}
                <div class="cert-group">
                    <strong>{{ provider_group.grouper }}:</strong>
                    {% for cert in provider_group.list %}
                    <span class="cert-item">{{ cert.certification }}</span>{% if not loop.last %}, {% endif %}
                    {% endfor %}
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}

        <!-- Professional Experience -->
        {% if data.experience %}
        <section class="section">
            <h2 class="section-title">Experience</h2>
            <div class="section-content">
                {% for job in data.experience %}
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">{{ job.title }}</h3>
                        {% if job.start_date and job.end_date %}
                        <span class="duration">{{ job.start_date }} – {{ job.end_date }}</span>
                        {% endif %}
                    </div>
                    <p class="company">{{ job.company }}</p>
                    {% if job.responsibilities %}
                    <ul class="responsibilities">
                        {% for resp in job.responsibilities %}
                        <li>{{ resp }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}

        <!-- Honor Awards -->
        {% if data.honor_awards %}
        <section class="section">
            <h2 class="section-title">Honor Awards</h2>
            <div class="section-content">
                <ul class="awards-list">
                    {% for award in data.honor_awards %}
                    <li>{{ award }}</li>
                    {% endfor %}
                </ul>
            </div>
        </section>
        {% endif %}
    </div>
</body>
</html>
        """
        return Template(template_content)
    
    def _load_css(self) -> str:
        """Load CSS styles for the resume"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
        }

        .resume {
            max-width: 8.5in;
            margin: 0 auto;
            padding: 0.75in;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #2c3e50;
        }

        .name {
            font-size: 2.5rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            font-size: 1rem;
            color: #666;
        }

        .section {
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 1.3rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.75rem;
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #bdc3c7;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .section-content {
            padding-left: 0.5rem;
        }

        .job-target {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }

        .job-title {
            font-size: 1.2rem;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 0.25rem;
        }

        .company {
            font-size: 1rem;
            color: #666;
            margin-bottom: 0.25rem;
        }

        .job-location {
            font-size: 0.9rem;
            color: #888;
        }

        .skills-category {
            margin-bottom: 0.75rem;
            line-height: 1.6;
        }

        .skills-category strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .skills-list {
            margin-left: 0.5rem;
        }

        .cert-group {
            margin-bottom: 0.75rem;
            line-height: 1.6;
        }

        .cert-group strong {
            color: #2c3e50;
            font-weight: 600;
        }

        .cert-item {
            margin-left: 0.5rem;
        }

        .experience-item {
            margin-bottom: 1.5rem;
            page-break-inside: avoid;
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .duration {
            font-size: 0.9rem;
            color: #666;
            font-weight: bold;
        }

        .responsibilities {
            margin-top: 0.5rem;
            padding-left: 1.5rem;
        }

        .responsibilities li {
            margin-bottom: 0.25rem;
            font-size: 0.95rem;
        }

        .education-item {
            margin-bottom: 1rem;
        }

        .degree {
            font-size: 1.1rem;
            font-weight: bold;
            color: #2c3e50;
        }

        .school {
            color: #666;
            margin-bottom: 0.25rem;
        }

        .graduation-year {
            font-size: 0.9rem;
            color: #888;
        }

        .awards-list {
            list-style: none;
            padding-left: 0;
        }

        .awards-list li {
            background: #e8f5e8;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            border-radius: 3px;
            border-left: 3px solid #27ae60;
        }

        @media print {
            .resume {
                margin: 0;
                padding: 0.5in;
            }
            
            .section {
                page-break-inside: avoid;
            }
        }

        @page {
            size: letter;
            margin: 0.5in;
        }
        """
    
    def generate_html(self, data: Dict) -> str:
        """Generate HTML resume from parsed data"""
        return self.template.render(data=data, css=self.css)
