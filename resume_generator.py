import os
from jinja2 import Template
from weasyprint import HTM<PERSON>, CSS
from typing import Dict

class ResumeGenerator:
    def __init__(self):
        self.template = self._load_template()
        self.css = self._load_css()
    
    def _load_template(self) -> Template:
        """Load the HTML template for the resume"""
        template_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume - {{ data.name }}</title>
    <style>{{ css }}</style>
</head>
<body>
    <div class="resume">
        <!-- Header Section -->
        <header class="header">
            <h1 class="name">{{ data.name }}</h1>
            <div class="contact-info">
                <span class="email">📧 {{ data.email }}</span>
                <span class="phone">📱 {{ data.phone }}</span>
                <span class="location">📍 {{ data.location }}</span>
            </div>
        </header>

        <!-- Professional Summary -->
        {% if data.summary %}
        <section class="section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="section-content">
                <div class="summary-text">{{ data.summary }}</div>
            </div>
        </section>
        {% endif %}

        <!-- Technical Skills -->
        {% if data.technical_skills %}
        <section class="section">
            <h2 class="section-title">Technical Skills</h2>
            <div class="section-content">
                <div class="skills-grid">
                    {% for category, skills in data.technical_skills.items() %}
                    <div class="skills-category">
                        <strong>{{ category }}</strong>
                        <div class="skills-list">{{ skills | join(', ') }}</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% endif %}

        <!-- Certifications -->
        {% if data.certifications %}
        <section class="section">
            <h2 class="section-title">Certifications</h2>
            <div class="section-content">
                <div class="cert-grid">
                    {% for provider_group in data.certifications | groupby('provider') %}
                    <div class="cert-group">
                        <strong>{{ provider_group.grouper }}</strong>
                        <div>
                            {% for cert in provider_group.list %}
                            <span class="cert-item">{{ cert.certification }}</span>{% if not loop.last %}, {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </section>
        {% endif %}

        <!-- Professional Experience -->
        {% if data.experience %}
        <section class="section">
            <h2 class="section-title">Professional Experience</h2>
            <div class="section-content">
                {% for job in data.experience %}
                <div class="experience-item">
                    <div class="experience-header">
                        <h3 class="job-title">{{ job.title }}</h3>
                        {% if job.start_date and job.end_date %}
                        <span class="duration">{{ job.start_date }} – {{ job.end_date }}</span>
                        {% endif %}
                    </div>
                    <p class="company">{{ job.company }}</p>
                    {% if job.responsibilities %}
                    <ul class="responsibilities">
                        {% for resp in job.responsibilities %}
                        <li>{{ resp }}</li>
                        {% endfor %}
                    </ul>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </section>
        {% endif %}

        <!-- Honor Awards -->
        {% if data.honor_awards %}
        <section class="section">
            <h2 class="section-title">Honor Awards</h2>
            <div class="section-content">
                <ul class="awards-list">
                    {% for award in data.honor_awards %}
                    <li>{{ award }}</li>
                    {% endfor %}
                </ul>
            </div>
        </section>
        {% endif %}
    </div>
</body>
</html>
        """
        return Template(template_content)
    
    def _load_css(self) -> str:
        """Load CSS styles for the resume"""
        return """
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.4;
            color: #2c3e50;
            background: white;
            font-size: 11pt;
            margin: 0;
            padding: 0;
        }

        .resume {
            max-width: 8.5in;
            margin: 0 auto;
            background: white;
            padding: 0.75in;
        }

        .header {
            text-align: center;
            margin-bottom: 2.5rem;
            padding: 2rem 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: -0.75in -0.75in 2.5rem -0.75in;
            padding: 2rem 0.75in;
        }

        .name {
            font-size: 2.8rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            font-size: 1rem;
            opacity: 0.95;
        }

        .contact-info span {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .section {
            margin-bottom: 2rem;
            page-break-inside: avoid;
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #667eea;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: #764ba2;
        }

        .section-content {
            padding-left: 0.5rem;
        }

        .summary-text {
            font-size: 1.1rem;
            line-height: 1.7;
            text-align: justify;
            color: #34495e;
            background: #f8f9fa;
            padding: 1.5rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            font-style: italic;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .skills-category {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .skills-category strong {
            color: #667eea;
            font-weight: 600;
            font-size: 1rem;
            display: block;
            margin-bottom: 0.5rem;
        }

        .skills-list {
            color: #34495e;
            line-height: 1.6;
        }

        .cert-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .cert-group {
            background: #e8f5e8;
            padding: 1rem;
            border-radius: 8px;
            border-left: 4px solid #27ae60;
        }

        .cert-group strong {
            color: #27ae60;
            font-weight: 600;
            display: block;
            margin-bottom: 0.5rem;
        }

        .cert-item {
            color: #2c3e50;
        }

        .experience-item {
            margin-bottom: 2rem;
            page-break-inside: avoid;
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }

        .experience-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 0.5rem;
            flex-wrap: wrap;
        }

        .job-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #667eea;
            margin-bottom: 0.25rem;
            flex: 1;
        }

        .duration {
            font-size: 0.95rem;
            color: #7f8c8d;
            font-weight: 600;
            background: #ecf0f1;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            white-space: nowrap;
        }

        .company {
            font-size: 1.1rem;
            color: #34495e;
            font-weight: 500;
            margin-bottom: 1rem;
        }

        .responsibilities {
            margin-top: 0.75rem;
            padding-left: 0;
            list-style: none;
        }

        .responsibilities li {
            margin-bottom: 0.5rem;
            font-size: 1rem;
            line-height: 1.6;
            color: #2c3e50;
            position: relative;
            padding-left: 1.5rem;
        }

        .responsibilities li::before {
            content: '▸';
            color: #667eea;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .awards-list {
            list-style: none;
            padding-left: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }

        .awards-list li {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            padding: 1rem;
            border-radius: 8px;
            color: #2c3e50;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .page-number {
            position: absolute;
            bottom: 0.5in;
            right: 0.75in;
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .experience-item {
            page-break-inside: avoid;
            margin-bottom: 1.5rem;
        }

        @page {
            size: letter;
            margin: 0.75in;
        }
        """
    
    def generate_html(self, data: Dict) -> str:
        """Generate HTML resume from parsed data"""
        return self.template.render(data=data, css=self.css)

    def generate_pdf(self, html_content: str, filename: str) -> str:
        """Generate PDF from HTML content using WeasyPrint"""
        # Create uploads directory if it doesn't exist
        os.makedirs('uploads', exist_ok=True)

        # Full path for the PDF
        pdf_path = os.path.join('uploads', filename)

        try:
            # Generate PDF using WeasyPrint
            html_doc = HTML(string=html_content)
            html_doc.write_pdf(pdf_path)
            return pdf_path
        except Exception as e:
            print(f"WeasyPrint error: {e}")
            raise Exception(f"Failed to generate PDF: {str(e)}")
