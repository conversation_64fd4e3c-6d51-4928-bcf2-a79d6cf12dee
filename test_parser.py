#!/usr/bin/env python3
"""
Simple test script to verify the resume parser functionality
"""

from resume_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>

def test_parser():
    parser = ResumeParser()
    
    # Test job posting from the example
    test_job_posting = """Cybersecurity Engineer II
Location: Remote (US-based)
Company: Novashield Technologies

DESCRIPTION
We're seeking a Cybersecurity Engineer with hands-on experience in threat detection, response automation, and cloud security hardening. You'll help build detection pipelines, fine-tune alerts, and integrate our SOAR platform with third-party APIs.

RESPONSIBILITIES
- Build and maintain threat detection rules (YARA, Sigma, Suricata)
- Integrate and manage SOAR tools (e.g., Cortex XSOAR, Shuffle) to automate response actions
- Harden AWS cloud infrastructure using Infrastructure as Code (Terraform preferred)
- Conduct incident response and perform threat hunting using Elastic Stack
- Work cross-functionally with DevOps to implement least privilege IAM policies

REQUIREMENTS
- 5+ years in a security engineering role
- Experience with SOAR platforms and custom playbook creation
- Familiarity with AWS security services (GuardDuty, IAM, KMS)
- Strong scripting in Python and shell environments
- Bonus: Experience with malware analysis and reverse engineering

BENEFITS
- $125,000–$145,000/year + bonus
- Full remote flexibility
- Education & certification stipend
- 401(k) match + stock options"""

    # Parse the job posting
    result = parser.parse_job_posting(test_job_posting)
    
    # Print results
    print("=== PARSED JOB POSTING ===")
    print(f"Title: {result['title']}")
    print(f"Company: {result['company']}")
    print(f"Location: {result['location']}")
    print(f"Description: {result['description'][:100]}...")
    
    print(f"\nResponsibilities ({len(result['responsibilities'])} items):")
    for i, resp in enumerate(result['responsibilities'], 1):
        print(f"  {i}. {resp}")
    
    print(f"\nRequirements ({len(result['requirements'])} items):")
    for i, req in enumerate(result['requirements'], 1):
        print(f"  {i}. {req}")
    
    print(f"\nBenefits ({len(result['benefits'])} items):")
    for i, benefit in enumerate(result['benefits'], 1):
        print(f"  {i}. {benefit}")
    
    # Test salary extraction
    salary = parser.extract_salary_range(test_job_posting)
    print(f"\nSalary Range: {salary}")
    
    # Test experience extraction
    experience = parser.extract_experience_level(test_job_posting)
    print(f"Experience Level: {experience}")
    
    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    test_parser()
