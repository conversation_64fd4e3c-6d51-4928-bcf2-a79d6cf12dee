#!/usr/bin/env python3
"""
Simple test script to verify the resume parser functionality
"""

from resume_parser import <PERSON>sume<PERSON>ars<PERSON>

def test_parser():
    parser = ResumeParser()

    # Test resume from the example
    test_resume = """Andrew Arz
Email: <EMAIL>
Phone: ************
Location: Knoxville, United States

SUMMARY
Cyber Security Expert and United States Navy veteran with 20 years of proven experience. A strong hands-on approach, can manage diverse projects simultaneously and with excellent communication skills while leading or working with teams in a dynamic, fast-paced environment. A self-starter and quick learner, always looking to increase my knowledge in the IT and Cyber Security fields. Department of Energy Q Clearance.

TECHNICAL SKILLS
Security Tools: Wazuh/OSSEC, MISP, PeStudio, Trivy, Kubebench, Yara
Languages: YAML (GitLab CI/CD), Ansible
Frameworks: NIST 800-53, RMF, SSDF
Cloud: AWS (EC2, S3, RDS, CloudFront), Azure
Automation: GitLab CI/CD, Ansible, Kubernetes, Docker
Other Skills: Virtualization (Hyper-V, Proxmox), OpenSSL, Firewall Management, Vulnerability Management, DevSecOps, Threat Intelligence, Container Security, Malware Analysis, Elasticsearch with Kibana, auditd, CIS Benchmarks

CERTIFICATIONS
CompTIA: A+, Network+, Security+, Linux+, SecurityX (Formerly CASP)
AWS: Solutions Architect - Associate Level
Microsoft: Microsoft Certified Solutions Associate

EXPERIENCE

Cybersecurity Engineer – Research and Development
Sandia National Laboratories – 02/2023 – Present
- Engineered and implemented robust security integrations into GitLab CI/CD pipeline across 150+ projects, transforming DevOps workflows into a comprehensive DevSecOps framework
- Architected and deployed hundreds of security controls across Azure, AWS, GitLab, Artifactory, and SonarQube to achieve full compliance with NIST SSDF
- Spearheaded development of CISA's open-source "Logging Made Easy" project, driving architectural improvements for the 2.0 release
- Engineered scalable big data parsing solutions processing telemetry from hundreds of government agencies using AWS S3 and EMR

Cyber Security Engineer / IT Team Lead – Exascale Computing Project
Oak Ridge National Laboratory / UT Battelle – 01/2022 – 02/2023
- Spearheaded cybersecurity operations for a $2B Exascale Computing Project, advancing to IT Team Lead managing a team of 4 IT professionals
- Architected and managed comprehensive AWS infrastructure including VPCs, security groups, EC2, CloudFront, Load Balancers, Certificate Manager, RDS, S3, and backups
- Implemented 150+ auditd rules for Linux and deployed Wazuh SIEM with active response
- Automated vulnerability management with Nessus and Ansible

HONOR AWARDS
Honorable Discharge – United States Navy
Navy Achievement Medal (2 awards) – United States Navy
Navy Commendation Medal (2 awards) – United States Navy"""

    # Parse the resume
    result = parser.parse_resume(test_resume)

    # Debug: Check what sections were found
    import re
    skills_match = re.search(parser.section_patterns['technical_skills'], test_resume, re.DOTALL | re.IGNORECASE)
    if skills_match:
        print("=== DEBUG: TECHNICAL SKILLS SECTION ===")
        print(repr(skills_match.group(1)))
        print("=" * 40)

    exp_match = re.search(parser.section_patterns['experience'], test_resume, re.DOTALL | re.IGNORECASE)
    if exp_match:
        print("=== DEBUG: EXPERIENCE SECTION ===")
        print(repr(exp_match.group(1)))
        print("=" * 40)

    # Print results
    print("=== PARSED RESUME ===")
    print(f"Name: {result['name']}")
    print(f"Email: {result['email']}")
    print(f"Phone: {result['phone']}")
    print(f"Location: {result['location']}")
    print(f"Summary: {result['summary'][:100]}...")

    print(f"\nTechnical Skills ({len(result['technical_skills'])} categories):")
    for category, skills in result['technical_skills'].items():
        print(f"  {category}: {', '.join(skills[:3])}{'...' if len(skills) > 3 else ''}")

    print(f"\nCertifications ({len(result['certifications'])} items):")
    for cert in result['certifications']:
        print(f"  {cert['provider']}: {cert['certification']}")

    print(f"\nExperience ({len(result['experience'])} positions):")
    for job in result['experience']:
        print(f"  {job['title']} at {job['company']}")
        print(f"    {job['start_date']} - {job['end_date']}")
        print(f"    {len(job['responsibilities'])} responsibilities")

    print(f"\nHonor Awards ({len(result['honor_awards'])} items):")
    for award in result['honor_awards']:
        print(f"  {award}")

    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    test_parser()
