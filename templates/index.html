<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resume Generator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: #2c3e50;
            color: white;
            padding: 2rem;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .form-container {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #2c3e50;
        }

        textarea {
            width: 100%;
            min-height: 400px;
            padding: 1rem;
            border: 2px solid #e0e0e0;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            resize: vertical;
            transition: border-color 0.3s ease;
        }

        textarea:focus {
            outline: none;
            border-color: #3498db;
        }

        .button-group {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .btn {
            padding: 1rem 2rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #95a5a6;
            color: white;
        }

        .btn-secondary:hover {
            background: #7f8c8d;
            transform: translateY(-2px);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 2rem;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 1rem;
            margin-top: 1rem;
            font-size: 0.9rem;
        }

        .example h4 {
            color: #2c3e50;
            margin-bottom: 0.5rem;
        }

        .example-text {
            font-family: 'Courier New', monospace;
            background: white;
            padding: 0.5rem;
            border-radius: 3px;
            white-space: pre-line;
            font-size: 0.8rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Resume Generator</h1>
            <p>Paste a job posting and generate a perfectly formatted resume</p>
        </div>

        <div class="form-container">
            <form id="resumeForm">
                <div class="form-group">
                    <label for="job_posting">Job Posting Text:</label>
                    <textarea 
                        id="job_posting" 
                        name="job_posting" 
                        placeholder="Paste the job posting here..."
                        required
                    ></textarea>
                </div>

                <div class="button-group">
                    <button type="button" class="btn btn-secondary" onclick="previewResume()">Preview HTML</button>
                    <button type="submit" class="btn btn-primary">Generate PDF</button>
                </div>
            </form>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Generating your resume...</p>
            </div>

            <div class="example">
                <h4>Example Job Posting Format:</h4>
                <div class="example-text">Cybersecurity Engineer II
Location: Remote (US-based)
Company: Novashield Technologies

DESCRIPTION
We're seeking a Cybersecurity Engineer with hands-on experience...

RESPONSIBILITIES
- Build and maintain threat detection rules
- Integrate and manage SOAR tools
- Harden AWS cloud infrastructure

REQUIREMENTS
- 5+ years in a security engineering role
- Experience with SOAR platforms
- Familiarity with AWS security services

BENEFITS
- $125,000–$145,000/year + bonus
- Full remote flexibility
- Education & certification stipend</div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('resumeForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateResume();
        });

        function generateResume() {
            const formData = new FormData(document.getElementById('resumeForm'));
            const loading = document.getElementById('loading');
            
            loading.style.display = 'block';
            
            fetch('/generate', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Failed to generate resume');
                }
            })
            .then(blob => {
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'resume.pdf';
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                loading.style.display = 'none';
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating resume. Please try again.');
                loading.style.display = 'none';
            });
        }

        function previewResume() {
            const formData = new FormData(document.getElementById('resumeForm'));
            
            fetch('/preview', {
                method: 'POST',
                body: formData
            })
            .then(response => response.text())
            .then(html => {
                const newWindow = window.open();
                newWindow.document.write(html);
                newWindow.document.close();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating preview. Please try again.');
            });
        }
    </script>
</body>
</html>
