from flask import Flask, render_template, request, send_file, jsonify
import os
import tempfile
from datetime import datetime
from resume_parser import ResumeParser
from resume_generator import ResumeGenerator

app = Flask(__name__)

# Initialize components
parser = ResumeParser()
generator = ResumeGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_resume():
    try:
        # Get resume text from form
        resume_text = request.form.get('resume_text', '').strip()

        if not resume_text:
            return jsonify({'error': 'Please provide resume text'}), 400

        # Parse the resume
        parsed_data = parser.parse_resume(resume_text)

        # Generate HTML resume
        html_content = generator.generate_html(parsed_data)

        # Generate PDF
        pdf_filename = f"resume_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        pdf_path = generator.generate_pdf(html_content, pdf_filename)

        # Return the PDF file
        return send_file(
            pdf_path,
            as_attachment=True,
            download_name=pdf_filename,
            mimetype='application/pdf'
        )

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/preview', methods=['POST'])
def preview_resume():
    try:
        # Get resume text from form
        resume_text = request.form.get('resume_text', '').strip()

        if not resume_text:
            return jsonify({'error': 'Please provide resume text'}), 400

        # Parse the resume
        parsed_data = parser.parse_resume(resume_text)

        # Generate HTML resume
        html_content = generator.generate_html(parsed_data)

        return html_content

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    # Create uploads directory if it doesn't exist
    os.makedirs('uploads', exist_ok=True)
    app.run(host='0.0.0.0', port=5000, debug=True)
