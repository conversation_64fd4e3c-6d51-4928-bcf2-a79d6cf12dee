from flask import Flask, render_template, request, jsonify
from resume_parser import ResumeParser
from resume_generator import ResumeGenerator

app = Flask(__name__)

# Initialize components
parser = ResumeParser()
generator = ResumeGenerator()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/generate', methods=['POST'])
def generate_html():
    try:
        # Get resume text from form
        resume_text = request.form.get('resume_text', '').strip()

        if not resume_text:
            return render_template('index.html', error='Please provide resume text')

        # Parse the resume
        parsed_data = parser.parse_resume(resume_text)

        # Generate HTML resume
        html_content = generator.generate_html(parsed_data)

        return html_content

    except Exception as e:
        return render_template('index.html', error=str(e))

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
