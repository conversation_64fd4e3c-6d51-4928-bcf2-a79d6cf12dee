<PERSON> Arz  
Email: and<PERSON><PERSON><PERSON>@gmail.com  
Phone: ************  
Location: Knoxville, United States  

SUMMARY  
Cyber Security Expert and United States Navy veteran with 20 years of proven experience. A strong hands-on approach, can manage diverse projects simultaneously and with excellent communication skills while leading or working with teams in a dynamic, fast-paced environment. A self-starter and quick learner, always looking to increase my knowledge in the IT and Cyber Security fields. Department of Energy Q Clearance.

TECHNICAL SKILLS  
Security Tools: Wazuh/OSSEC, MISP, PeStudio, Trivy, Kubebench, Yara  
Languages: YAML (GitLab CI/CD), Ansible  
Frameworks: NIST 800-53, RMF, SSDF  
Cloud: AWS (EC2, S3, RDS, CloudFront), Azure  
Automation: GitLab CI/CD, Ansible, Kubernetes, Docker  
Other Skills: Virtualization (Hyper-V, Proxmox), OpenSSL, Firewall Management, Vulnerability Management, DevSecOps, Threat Intelligence, Container Security, Malware Analysis, Elasticsearch with Kibana, auditd, CIS Benchmarks

CERTIFICATIONS  
CompTIA: A+, Network+, Security+, Linux+, SecurityX (Formerly CASP)  
AWS: Solutions Architect - Associate Level  
Microsoft: Microsoft Certified Solutions Associate  

EXPERIENCE  

Cybersecurity Engineer – Research and Development  
Sandia National Laboratories – 02/2023 – Present  
- Engineered and implemented robust security integrations into GitLab CI/CD pipeline across 150+ projects, transforming DevOps workflows into a comprehensive DevSecOps framework  
- Architected and deployed hundreds of security controls across Azure, AWS, GitLab, Artifactory, and SonarQube to achieve full compliance with NIST SSDF  
- Spearheaded development of CISA's open-source "Logging Made Easy" project, driving architectural improvements for the 2.0 release  
- Engineered scalable big data parsing solutions processing telemetry from hundreds of government agencies using AWS S3 and EMR  

Cyber Security Engineer / IT Team Lead – Exascale Computing Project  
Oak Ridge National Laboratory / UT Battelle – 01/2022 – 02/2023  
- Spearheaded cybersecurity operations for a $2B Exascale Computing Project, advancing to IT Team Lead managing a team of 4 IT professionals  
- Architected and managed comprehensive AWS infrastructure including VPCs, security groups, EC2, CloudFront, Load Balancers, Certificate Manager, RDS, S3, and backups  
- Implemented 150+ auditd rules for Linux and deployed Wazuh SIEM with active response  
- Automated vulnerability management with Nessus and Ansible  

Cyber Policy Analyst  
Oak Ridge National Laboratory / Information International Associates – 09/2020 – 01/2022  
- Wrote and updated Systems Security Plans meeting NIST 800-53 and ORNL requirements  
- Engineered secure cloud microservices in DevSecOps environments  
- Configured GitLab CI/CD security architectures with SAST, DAST, and container scanning  
- Researched open-source threat intel and implemented continuous monitoring tools  

Senior Instructor  
United States Navy – Information Warfare Training Command – 07/2017 – 09/2020  
- Instructed 350 students on Information Systems Security Manager policies and procedures  
- Acted as SME for cybersecurity curriculum updates  
- Oversaw installation of a new training network with 70 workstations  
- Managed 20 instructors supporting IT/Cyber courses  

Information Systems Security Manager  
United States Navy – USS Philippine Sea – 06/2013 – 07/2017  
- Managed 16 cybersecurity and IT professionals, overseeing 20 virtual Hyper-V servers and 400 network devices  
- Maintained cyber security posture, drafted policies, and conducted cyber awareness training  
- Performed scans, patch management, and monitored SIEM, firewalls, IDS/IPS, proxies, and routers  

Network Operations Center Analyst  
United States Navy – Navy Marine Corps Intranet – 11/2009 – 06/2013  
- Monitored 1,000+ network servers and resolved Tier 2/3 tickets  
- Conducted cybersecurity vulnerability scans and in-person issue resolutions  

Technical Support Supervisor  
United States Navy – USS Kearsarge – 05/2007 – 11/2009  
- Managed all communications including radio, satellite, and IP services  
- Led 4-person watch team on 12-hour shifts maintaining cisco routers, linux servers, and encrypted comms  

Service Desk Agent  
United States Navy – USS Kearsarge – 05/2005 – 05/2007  
- Provided Tier 1 IT/Cyber support for 1200 sailors and 2000 marines  
- Handled patch management for Windows and Linux devices  
- Set up mobile networks with switches, routers, and ACLs for Marine deployments  

HONOR AWARDS  
Honorable Discharge – United States Navy  
Navy Achievement Medal (2 awards) – United States Navy  
Navy Commendation Medal (2 awards) – United States Navy  
