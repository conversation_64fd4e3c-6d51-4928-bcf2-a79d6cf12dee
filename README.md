# Simple Resume Generator

A Docker-based application that generates beautifully formatted PDF resumes from job posting text.

## Features

- **Simple Interface**: Just paste a job posting and click generate
- **Smart Parsing**: Automatically extracts job title, company, location, requirements, and responsibilities
- **Professional Templates**: Clean, ATS-friendly resume layouts
- **PDF Generation**: High-quality PDF output ready for printing or digital submission
- **Docker-based**: No need to install dependencies on your host machine
- **Preview Mode**: See HTML preview before generating PDF

## Quick Start

1. **Build and run the application:**
   ```bash
   docker-compose up --build
   ```

2. **Open your browser and navigate to:**
   ```
   http://localhost:5000
   ```

3. **Paste your job posting text and click "Generate PDF"**

## Usage

### Job Posting Format

The application works best with job postings that follow this structure:

```
Job Title
Location: [Location]
Company: [Company Name]

DESCRIPTION
[Job description text]

RESPONSIBILITIES
- [Responsibility 1]
- [Responsibility 2]
- [etc.]

REQUIREMENTS
- [Requirement 1]
- [Requirement 2]
- [etc.]

BENEFITS
- [Benefit 1]
- [Benefit 2]
- [etc.]
```

### Example

```
Cybersecurity Engineer II
Location: Remote (US-based)
Company: Novashield Technologies

DESCRIPTION
We're seeking a Cybersecurity Engineer with hands-on experience in threat detection, response automation, and cloud security hardening.

RESPONSIBILITIES
- Build and maintain threat detection rules (YARA, Sigma, Suricata)
- Integrate and manage SOAR tools (e.g., Cortex XSOAR, Shuffle)
- Harden AWS cloud infrastructure using Infrastructure as Code

REQUIREMENTS
- 5+ years in a security engineering role
- Experience with SOAR platforms and custom playbook creation
- Familiarity with AWS security services (GuardDuty, IAM, KMS)

BENEFITS
- $125,000–$145,000/year + bonus
- Full remote flexibility
- Education & certification stipend
```

## Development

### Project Structure

```
├── app.py                 # Main Flask application
├── resume_parser.py       # Job posting parser
├── resume_generator.py    # HTML/PDF generator
├── templates/
│   └── index.html        # Frontend interface
├── uploads/              # Generated PDF storage
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose setup
└── requirements.txt     # Python dependencies
```

### Local Development

If you want to run without Docker:

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

### Customization

- **Resume Template**: Edit the HTML template in `resume_generator.py`
- **Styling**: Modify the CSS in the `_load_css()` method
- **Parser Logic**: Update parsing patterns in `resume_parser.py`
- **Frontend**: Customize the interface in `templates/index.html`

## Technical Details

- **Backend**: Python Flask
- **PDF Generation**: WeasyPrint
- **Template Engine**: Jinja2
- **Frontend**: Vanilla HTML/CSS/JavaScript
- **Containerization**: Docker

## Troubleshooting

### Common Issues

1. **PDF generation fails**: Ensure all system dependencies are installed (handled automatically in Docker)
2. **Port already in use**: Change the port in `docker-compose.yml`
3. **Parsing issues**: Check that your job posting follows the expected format

### Logs

View application logs:
```bash
docker-compose logs -f
```

## License

This project is open source and available under the MIT License.
