# Simple Resume Formatter

A Docker-based application that converts plain text resumes into beautifully formatted PDF documents.

## Features

- **Simple Interface**: Just paste your resume text and click generate
- **Smart Parsing**: Automatically extracts personal info, summary, skills, certifications, experience, and awards
- **Professional Templates**: Clean, ATS-friendly resume layouts with proper formatting
- **PDF Generation**: High-quality PDF output ready for printing or digital submission
- **Docker-based**: No need to install dependencies on your host machine
- **Preview Mode**: See HTML preview before generating PDF

## Quick Start

1. **Build and run the application:**
   ```bash
   docker-compose up --build
   ```

2. **Open your browser and navigate to:**
   ```
   http://localhost:5000
   ```

3. **Paste your resume text and click "Generate PDF"**

## Usage

### Resume Format

The application works best with resumes that follow this structure:

```
[Your Name]
Email: [<EMAIL>]
Phone: [your phone number]
Location: [your location]

SUMMARY
[Your professional summary]

TECHNICAL SKILLS
[Category]: [skill1, skill2, skill3]
[Category]: [skill1, skill2, skill3]

CERTIFICATIONS
[Provider]: [cert1, cert2, cert3]
[Provider]: [cert1, cert2]

EXPERIENCE

[Job Title]
[Company Name] – [Start Date] – [End Date]
- [Responsibility/achievement 1]
- [Responsibility/achievement 2]
- [Responsibility/achievement 3]

[Job Title]
[Company Name] – [Start Date] – [End Date]
- [Responsibility/achievement 1]
- [Responsibility/achievement 2]

HONOR AWARDS
[Award 1]
[Award 2]
```

### Example

```
Andrew Arz
Email: <EMAIL>
Phone: ************
Location: Knoxville, United States

SUMMARY
Cyber Security Expert and United States Navy veteran with 20 years of proven experience. A strong hands-on approach, can manage diverse projects simultaneously and with excellent communication skills.

TECHNICAL SKILLS
Security Tools: Wazuh/OSSEC, MISP, PeStudio, Trivy, Kubebench, Yara
Languages: YAML (GitLab CI/CD), Ansible
Cloud: AWS (EC2, S3, RDS, CloudFront), Azure

CERTIFICATIONS
CompTIA: A+, Network+, Security+, Linux+, SecurityX
AWS: Solutions Architect - Associate Level

EXPERIENCE

Cybersecurity Engineer – Research and Development
Sandia National Laboratories – 02/2023 – Present
- Engineered and implemented robust security integrations into GitLab CI/CD pipeline
- Architected and deployed hundreds of security controls across Azure, AWS, GitLab
- Spearheaded development of CISA's open-source "Logging Made Easy" project

HONOR AWARDS
Honorable Discharge – United States Navy
Navy Achievement Medal (2 awards) – United States Navy
```

## Development

### Project Structure

```
├── app.py                 # Main Flask application
├── resume_parser.py       # Resume text parser
├── resume_generator.py    # HTML/PDF generator
├── templates/
│   └── index.html        # Frontend interface
├── uploads/              # Generated PDF storage
├── Dockerfile           # Docker configuration
├── docker-compose.yml   # Docker Compose setup
└── requirements.txt     # Python dependencies
```

### Local Development

If you want to run without Docker:

1. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python app.py
   ```

### Customization

- **Resume Template**: Edit the HTML template in `resume_generator.py`
- **Styling**: Modify the CSS in the `_load_css()` method
- **Parser Logic**: Update parsing patterns in `resume_parser.py` for different resume formats
- **Frontend**: Customize the interface in `templates/index.html`

## Technical Details

- **Backend**: Python Flask
- **PDF Generation**: WeasyPrint
- **Template Engine**: Jinja2
- **Frontend**: Vanilla HTML/CSS/JavaScript
- **Containerization**: Docker

## Troubleshooting

### Common Issues

1. **PDF generation fails**: Ensure all system dependencies are installed (handled automatically in Docker)
2. **Port already in use**: Change the port in `docker-compose.yml` (currently set to 5001:5000)
3. **Parsing issues**: Check that your resume follows the expected format with proper section headers

### Logs

View application logs:
```bash
docker-compose logs -f
```

## License

This project is open source and available under the MIT License.
