import re
from typing import Dict, List, Optional

class ResumeParser:
    def __init__(self):
        self.section_patterns = {
            'title': r'^([A-Za-z\s\-&/]+(?:I{1,3}|IV|V)?)\s*$',
            'location': r'Location:\s*(.+?)(?:\n|$)',
            'company': r'Company:\s*(.+?)(?:\n|$)',
            'description': r'DESCRIPTION\s*\n(.*?)(?=\n[A-Z]{2,}|\n\n|\Z)',
            'responsibilities': r'RESPONSIBILITIES\s*\n(.*?)(?=\n[A-Z]{2,}|\n\n|\Z)',
            'requirements': r'REQUIREMENTS\s*\n(.*?)(?=\n[A-Z]{2,}|\n\n|\Z)',
            'benefits': r'BENEFITS\s*\n(.*?)(?=\n[A-Z]{2,}|\n\n|\Z)'
        }
    
    def parse_job_posting(self, text: str) -> Dict:
        """Parse job posting text and extract structured data"""
        # Clean up the text
        text = text.strip()
        lines = text.split('\n')
        
        # Initialize result
        result = {
            'title': '',
            'location': '',
            'company': '',
            'description': '',
            'responsibilities': [],
            'requirements': [],
            'benefits': []
        }
        
        # Extract title (first line)
        if lines:
            title_match = re.match(self.section_patterns['title'], lines[0].strip())
            if title_match:
                result['title'] = title_match.group(1).strip()
        
        # Extract location and company
        for pattern_name in ['location', 'company']:
            match = re.search(self.section_patterns[pattern_name], text, re.IGNORECASE)
            if match:
                result[pattern_name] = match.group(1).strip()
        
        # Extract description
        desc_match = re.search(self.section_patterns['description'], text, re.DOTALL | re.IGNORECASE)
        if desc_match:
            result['description'] = desc_match.group(1).strip()
        
        # Extract list sections (responsibilities, requirements, benefits)
        for section in ['responsibilities', 'requirements', 'benefits']:
            match = re.search(self.section_patterns[section], text, re.DOTALL | re.IGNORECASE)
            if match:
                section_text = match.group(1).strip()
                # Split by bullet points or dashes
                items = re.split(r'\n\s*[-•]\s*', section_text)
                # Clean up items
                items = [item.strip() for item in items if item.strip()]
                # Remove leading dash from first item if present
                if items and items[0].startswith('-'):
                    items[0] = items[0][1:].strip()
                result[section] = items
        
        return result
    
    def extract_salary_range(self, text: str) -> Optional[str]:
        """Extract salary information from text"""
        salary_patterns = [
            r'\$[\d,]+(?:\s*[-–]\s*\$[\d,]+)?(?:/year|/yr|annually)?',
            r'[\d,]+k?\s*[-–]\s*[\d,]+k?(?:/year|/yr|annually)?'
        ]
        
        for pattern in salary_patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(0)
        
        return None
    
    def extract_experience_level(self, text: str) -> Optional[str]:
        """Extract experience level from requirements"""
        exp_pattern = r'(\d+)\+?\s*years?\s*(?:of\s*)?(?:experience|exp|in)'
        match = re.search(exp_pattern, text, re.IGNORECASE)
        if match:
            return f"{match.group(1)}+ years"
        return None
