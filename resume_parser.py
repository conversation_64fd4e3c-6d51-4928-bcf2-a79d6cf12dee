import re
from typing import Dict, List, Optional

class ResumeParser:
    def __init__(self):
        self.section_patterns = {
            'name': r'^([A-Za-z\s\-\.]+)\s*$',
            'email': r'Email:\s*(.+?)(?:\n|$)',
            'phone': r'Phone:\s*(.+?)(?:\n|$)',
            'location': r'Location:\s*(.+?)(?:\n|$)',
            'summary': r'SUMMARY\s*\n(.*?)(?=\n[A-Z][A-Z\s]+\n|\Z)',
            'technical_skills': r'TECHNICAL SKILLS\s*\n(.*?)(?=\n[A-Z][A-Z\s]+\n|\Z)',
            'certifications': r'CERTIFICATIONS\s*\n(.*?)(?=\n[A-Z][A-Z\s]+\n|\Z)',
            'experience': r'EXPERIENCE\s*\n(.*?)(?=\n[A-Z][A-Z\s]+\n|\Z)',
            'honor_awards': r'HONOR AWARDS\s*\n(.*?)(?=\Z)'
        }
    
    def parse_resume(self, text: str) -> Dict:
        """Parse resume text and extract structured data"""
        # Clean up the text
        text = text.strip()
        lines = text.split('\n')

        # Initialize result
        result = {
            'name': '',
            'email': '',
            'phone': '',
            'location': '',
            'summary': '',
            'technical_skills': {},
            'certifications': [],
            'experience': [],
            'honor_awards': []
        }

        # Extract name (first line)
        if lines:
            name_match = re.match(self.section_patterns['name'], lines[0].strip())
            if name_match:
                result['name'] = name_match.group(1).strip()

        # Extract contact info
        for pattern_name in ['email', 'phone', 'location']:
            match = re.search(self.section_patterns[pattern_name], text, re.IGNORECASE)
            if match:
                result[pattern_name] = match.group(1).strip()

        # Extract summary
        summary_match = re.search(self.section_patterns['summary'], text, re.DOTALL | re.IGNORECASE)
        if summary_match:
            result['summary'] = summary_match.group(1).strip()

        # Extract technical skills
        skills_match = re.search(self.section_patterns['technical_skills'], text, re.DOTALL | re.IGNORECASE)
        if skills_match:
            skills_text = skills_match.group(1).strip()
            result['technical_skills'] = self._parse_technical_skills(skills_text)

        # Extract certifications
        cert_match = re.search(self.section_patterns['certifications'], text, re.DOTALL | re.IGNORECASE)
        if cert_match:
            cert_text = cert_match.group(1).strip()
            result['certifications'] = self._parse_certifications(cert_text)

        # Extract experience
        exp_match = re.search(self.section_patterns['experience'], text, re.DOTALL | re.IGNORECASE)
        if exp_match:
            exp_text = exp_match.group(1).strip()
            result['experience'] = self._parse_experience(exp_text)

        # Extract honor awards
        awards_match = re.search(self.section_patterns['honor_awards'], text, re.DOTALL | re.IGNORECASE)
        if awards_match:
            awards_text = awards_match.group(1).strip()
            result['honor_awards'] = self._parse_awards(awards_text)

        return result

    def _parse_technical_skills(self, skills_text: str) -> Dict[str, List[str]]:
        """Parse technical skills section into categories"""
        skills_dict = {}
        lines = skills_text.split('\n')

        for line in lines:
            line = line.strip()
            if ':' in line and line:
                category, skills = line.split(':', 1)
                category = category.strip()
                skills_list = [skill.strip() for skill in skills.split(',') if skill.strip()]
                if skills_list:  # Only add if there are actual skills
                    skills_dict[category] = skills_list

        return skills_dict

    def _parse_certifications(self, cert_text: str) -> List[Dict[str, str]]:
        """Parse certifications section"""
        certifications = []
        lines = cert_text.split('\n')

        for line in lines:
            line = line.strip()
            if ':' in line:
                provider, certs = line.split(':', 1)
                provider = provider.strip()
                cert_list = [cert.strip() for cert in certs.split(',')]
                for cert in cert_list:
                    certifications.append({
                        'provider': provider,
                        'certification': cert
                    })

        return certifications

    def _parse_experience(self, exp_text: str) -> List[Dict[str, str]]:
        """Parse experience section"""
        experiences = []

        # Split by empty lines to get job blocks
        blocks = re.split(r'\n\s*\n', exp_text.strip())

        for block in blocks:
            block = block.strip()
            if not block:
                continue

            lines = [line.strip() for line in block.split('\n') if line.strip()]
            if len(lines) < 2:
                continue

            # First line is job title
            title = lines[0]

            # Second line contains company and dates
            company_date_line = lines[1]

            # Parse company and dates
            company = ""
            start_date = ""
            end_date = ""

            # Look for date pattern: MM/YYYY – MM/YYYY or MM/YYYY – Present
            date_match = re.search(r'(\d{2}/\d{4})\s*–\s*(.+?)$', company_date_line)
            if date_match:
                start_date = date_match.group(1)
                end_date = date_match.group(2)
                # Company is everything before the first date
                company = company_date_line[:date_match.start()].strip()
                # Remove trailing dash if present
                if company.endswith('–'):
                    company = company[:-1].strip()
            else:
                company = company_date_line

            # Extract responsibilities (lines starting with -)
            responsibilities = []
            for line in lines[2:]:
                if line.startswith('-'):
                    responsibilities.append(line[1:].strip())

            if title and company:  # Only add if we have at least title and company
                experiences.append({
                    'title': title,
                    'company': company,
                    'start_date': start_date,
                    'end_date': end_date,
                    'responsibilities': responsibilities
                })

        return experiences

    def _parse_awards(self, awards_text: str) -> List[str]:
        """Parse honor awards section"""
        awards = []
        lines = awards_text.split('\n')

        for line in lines:
            line = line.strip()
            if line:
                awards.append(line)

        return awards
